<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Test Suite - Chatbot Creator</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-3xl font-bold text-center mb-2">🧪 Supabase Test Suite</h1>
            <p class="text-center text-gray-600 mb-6">Comprehensive testing for Chatbot Creator backend</p>
            
            <!-- Configuration -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h2 class="font-semibold text-yellow-800 mb-2">⚙️ Configuration</h2>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Supabase URL</label>
                        <input type="text" id="supabaseUrl" class="w-full p-2 border border-gray-300 rounded-md text-sm"
                               value="https://fabdjjfrjwiqkcyjkyvy.supabase.co">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Anon Key</label>
                        <input type="password" id="supabaseKey" class="w-full p-2 border border-gray-300 rounded-md text-sm"
                               value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZhYmRqamZyandpcWtjeWpreXZ5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2OTU4MzMsImV4cCI6MjA2NjI3MTgzM30.yT3Y9XgTCb4zgPJl0NOfx3pEe6lI3HLw37FM6aKqADo">
                    </div>
                </div>
                <button onclick="initializeSupabase()" class="mt-3 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Initialize Connection
                </button>
                <button onclick="autoInitialize()" class="mt-3 ml-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    Auto Initialize & Test
                </button>
            </div>

            <!-- Test Results -->
            <div id="testResults" class="space-y-4"></div>

            <!-- Run Tests Button -->
            <div class="text-center mt-6">
                <button onclick="runAllTests()" id="runTestsBtn" class="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 disabled:bg-gray-400" disabled>
                    🚀 Run All Tests
                </button>
            </div>
        </div>
    </div>

    <script>
        let supabase = null;
        let testResults = [];

        function initializeSupabase() {
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('supabaseKey').value;

            if (!url || !key) {
                addTestResult('Configuration', '❌ Please enter both URL and key', 'error');
                return;
            }

            try {
                supabase = window.supabase.createClient(url, key);
                addTestResult('Configuration', '✅ Supabase client initialized', 'success');
                document.getElementById('runTestsBtn').disabled = false;
            } catch (error) {
                addTestResult('Configuration', `❌ Failed to initialize: ${error.message}`, 'error');
            }
        }

        function autoInitialize() {
            // Auto-initialize with pre-filled credentials and run tests
            initializeSupabase();
            setTimeout(() => {
                if (supabase) {
                    runAllTests();
                }
            }, 500);
        }

        function addTestResult(category, message, type = 'info') {
            const colors = {
                success: 'bg-green-50 border-green-200 text-green-800',
                error: 'bg-red-50 border-red-200 text-red-800',
                warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
                info: 'bg-blue-50 border-blue-200 text-blue-800'
            };

            const resultDiv = document.createElement('div');
            resultDiv.className = `border rounded-lg p-3 ${colors[type]}`;
            resultDiv.innerHTML = `<strong>${category}:</strong> ${message}`;
            
            document.getElementById('testResults').appendChild(resultDiv);
            
            // Auto-scroll to bottom
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        async function runAllTests() {
            if (!supabase) {
                addTestResult('Error', '❌ Please initialize Supabase connection first', 'error');
                return;
            }

            // Clear previous results
            document.getElementById('testResults').innerHTML = '';
            addTestResult('Test Suite', '🚀 Starting comprehensive tests...', 'info');

            // Test 1: Basic Connection
            await testConnection();
            
            // Test 2: Database Tables
            await testTables();
            
            // Test 3: Authentication
            await testAuthentication();
            
            // Test 4: Row Level Security
            await testRLS();
            
            // Test 5: Storage
            await testStorage();
            
            // Test 6: Functions
            await testFunctions();
            
            // Test 7: Seed Data
            await testSeedData();

            addTestResult('Test Suite', '🎉 All tests completed!', 'success');
        }

        async function testConnection() {
            try {
                addTestResult('Connection', '🔄 Testing basic connectivity...', 'info');
                
                const { data, error } = await supabase
                    .from('users')
                    .select('count')
                    .limit(1);

                if (error) {
                    addTestResult('Connection', `❌ Connection failed: ${error.message}`, 'error');
                } else {
                    addTestResult('Connection', '✅ Database connection successful', 'success');
                }
            } catch (error) {
                addTestResult('Connection', `❌ Connection error: ${error.message}`, 'error');
            }
        }

        async function testTables() {
            addTestResult('Tables', '🔄 Checking database tables...', 'info');
            
            const tables = ['users', 'chatbots', 'conversations', 'messages', 'analytics', 'knowledge_base', 'uploads', 'subscriptions'];
            let successCount = 0;

            for (const table of tables) {
                try {
                    const { error } = await supabase.from(table).select('count').limit(1);
                    if (error) {
                        addTestResult('Tables', `❌ Table '${table}': ${error.message}`, 'error');
                    } else {
                        addTestResult('Tables', `✅ Table '${table}' exists and accessible`, 'success');
                        successCount++;
                    }
                } catch (error) {
                    addTestResult('Tables', `❌ Table '${table}': ${error.message}`, 'error');
                }
            }

            addTestResult('Tables', `📊 Summary: ${successCount}/${tables.length} tables accessible`, 
                         successCount === tables.length ? 'success' : 'warning');
        }

        async function testAuthentication() {
            addTestResult('Authentication', '🔄 Testing auth system...', 'info');
            
            try {
                // Check current auth state
                const { data: { user } } = await supabase.auth.getUser();
                
                if (user) {
                    addTestResult('Authentication', `✅ User authenticated: ${user.email}`, 'success');
                    
                    // Test user profile access
                    const { data: profile, error: profileError } = await supabase
                        .from('users')
                        .select('*')
                        .eq('id', user.id)
                        .single();

                    if (profileError) {
                        addTestResult('Authentication', `⚠️ Profile access error: ${profileError.message}`, 'warning');
                    } else {
                        addTestResult('Authentication', `✅ User profile accessible: ${profile.email}`, 'success');
                    }
                } else {
                    addTestResult('Authentication', '⚠️ No user currently authenticated', 'warning');
                    addTestResult('Authentication', '💡 Sign in manually to test authenticated features', 'info');
                }
            } catch (error) {
                addTestResult('Authentication', `❌ Auth test error: ${error.message}`, 'error');
            }
        }

        async function testRLS() {
            addTestResult('Security (RLS)', '🔄 Testing Row Level Security...', 'info');

            try {
                // Test unauthenticated access (should be restricted)
                const { data: chatbots, error } = await supabase
                    .from('chatbots')
                    .select('*');

                if (error) {
                    if (error.message.includes('row-level security') ||
                        error.message.includes('policy') ||
                        error.code === 'PGRST116' ||
                        error.message.includes('insufficient privilege')) {
                        addTestResult('Security (RLS)', '✅ RLS working - unauthenticated access blocked', 'success');
                    } else {
                        addTestResult('Security (RLS)', `❌ RLS error: ${error.message}`, 'error');
                    }
                } else if (!chatbots || chatbots.length === 0) {
                    addTestResult('Security (RLS)', '✅ RLS working - no unauthorized data returned', 'success');
                } else {
                    addTestResult('Security (RLS)', '⚠️ RLS might not be properly configured', 'warning');
                    addTestResult('Security (RLS)', `   Found ${chatbots.length} accessible records`, 'info');
                }

                // Test public access for conversations (should work)
                const { data: conversations, error: convError } = await supabase
                    .from('conversations')
                    .select('id')
                    .limit(1);

                if (convError) {
                    addTestResult('Security (RLS)', `⚠️ Public conversation access blocked: ${convError.message}`, 'warning');
                } else {
                    addTestResult('Security (RLS)', '✅ Public conversation access working', 'success');
                }

            } catch (error) {
                addTestResult('Security (RLS)', `❌ RLS test error: ${error.message}`, 'error');
            }
        }

        async function testStorage() {
            addTestResult('Storage', '🔄 Testing file storage...', 'info');

            try {
                // First test basic storage access
                const { data: buckets, error } = await supabase.storage.listBuckets();

                if (error) {
                    addTestResult('Storage', `❌ Storage API error: ${error.message}`, 'error');
                    addTestResult('Storage', `Error code: ${error.status || 'unknown'}`, 'info');
                    return;
                }

                addTestResult('Storage', `✅ Storage API accessible. Found ${buckets.length} buckets`, 'success');

                // List all buckets for debugging
                if (buckets.length > 0) {
                    addTestResult('Storage', `Available buckets: ${buckets.map(b => b.name).join(', ')}`, 'info');
                } else {
                    addTestResult('Storage', 'No buckets found - this might be the issue', 'warning');
                }

                const chatbotBucket = buckets.find(bucket => bucket.name === 'chatbot-uploads');
                if (chatbotBucket) {
                    addTestResult('Storage', '✅ Storage bucket "chatbot-uploads" exists', 'success');
                    addTestResult('Storage', `   - Public: ${chatbotBucket.public}`, 'info');
                    addTestResult('Storage', `   - Created: ${new Date(chatbotBucket.created_at).toLocaleString()}`, 'info');

                    // Test file listing in the bucket
                    try {
                        const { data: files, error: filesError } = await supabase.storage
                            .from('chatbot-uploads')
                            .list('', { limit: 1 });

                        if (filesError) {
                            addTestResult('Storage', `⚠️ Bucket exists but file listing failed: ${filesError.message}`, 'warning');
                        } else {
                            addTestResult('Storage', `✅ Bucket file listing works (${files.length} files)`, 'success');
                        }
                    } catch (listError) {
                        addTestResult('Storage', `❌ File listing error: ${listError.message}`, 'error');
                    }
                } else {
                    addTestResult('Storage', '⚠️ Storage bucket "chatbot-uploads" not found in API', 'warning');

                    // Try to access the bucket directly anyway
                    try {
                        const { data: files, error: filesError } = await supabase.storage
                            .from('chatbot-uploads')
                            .list('', { limit: 1 });

                        if (!filesError) {
                            addTestResult('Storage', '✅ Bucket accessible directly (API listing issue)', 'success');
                        } else {
                            addTestResult('Storage', `❌ Direct bucket access failed: ${filesError.message}`, 'error');
                        }
                    } catch (directError) {
                        addTestResult('Storage', `❌ Direct bucket test failed: ${directError.message}`, 'error');
                    }
                }
            } catch (error) {
                addTestResult('Storage', `❌ Storage test error: ${error.message}`, 'error');
            }
        }

        async function testFunctions() {
            addTestResult('Functions', '🔄 Testing database functions...', 'info');
            
            try {
                const { data, error } = await supabase
                    .rpc('generate_embed_code', { chatbot_uuid: '550e8400-e29b-41d4-a716-************' });

                if (error) {
                    addTestResult('Functions', `❌ Function test failed: ${error.message}`, 'error');
                } else {
                    addTestResult('Functions', '✅ Database functions working', 'success');
                    addTestResult('Functions', `Generated embed code: ${data.substring(0, 50)}...`, 'info');
                }
            } catch (error) {
                addTestResult('Functions', `❌ Function test error: ${error.message}`, 'error');
            }
        }

        async function testSeedData() {
            addTestResult('Seed Data', '🔄 Checking seed data...', 'info');
            
            try {
                // Check users
                const { data: users, error: usersError } = await supabase
                    .from('users')
                    .select('id, email, plan_type')
                    .limit(5);

                if (usersError) {
                    addTestResult('Seed Data', `❌ Users query error: ${usersError.message}`, 'error');
                } else {
                    addTestResult('Seed Data', `✅ Found ${users.length} users`, 'success');
                    users.forEach(user => {
                        addTestResult('Seed Data', `   - ${user.email} (${user.plan_type})`, 'info');
                    });
                }

                // Check chatbots
                const { data: chatbots, error: chatbotsError } = await supabase
                    .from('chatbots')
                    .select('id, name, template_type, training_status')
                    .limit(5);

                if (chatbotsError) {
                    addTestResult('Seed Data', `❌ Chatbots query error: ${chatbotsError.message}`, 'error');
                } else {
                    addTestResult('Seed Data', `✅ Found ${chatbots.length} chatbots`, 'success');
                    chatbots.forEach(bot => {
                        addTestResult('Seed Data', `   - ${bot.name} (${bot.template_type}, ${bot.training_status})`, 'info');
                    });
                }

                // Check conversations
                const { data: conversations, error: conversationsError } = await supabase
                    .from('conversations')
                    .select('id, session_id, message_count, lead_captured')
                    .limit(5);

                if (conversationsError) {
                    addTestResult('Seed Data', `❌ Conversations query error: ${conversationsError.message}`, 'error');
                } else {
                    addTestResult('Seed Data', `✅ Found ${conversations.length} conversations`, 'success');
                    conversations.forEach(conv => {
                        addTestResult('Seed Data', `   - Session ${conv.session_id}: ${conv.message_count} messages, lead: ${conv.lead_captured}`, 'info');
                    });
                }

            } catch (error) {
                addTestResult('Seed Data', `❌ Seed data test error: ${error.message}`, 'error');
            }
        }

        // Auto-fill from localStorage if available
        window.onload = function() {
            const savedUrl = localStorage.getItem('supabase_url');
            const savedKey = localStorage.getItem('supabase_key');
            
            if (savedUrl) document.getElementById('supabaseUrl').value = savedUrl;
            if (savedKey) document.getElementById('supabaseKey').value = savedKey;

            // Save values when changed
            document.getElementById('supabaseUrl').addEventListener('change', function() {
                localStorage.setItem('supabase_url', this.value);
            });
            
            document.getElementById('supabaseKey').addEventListener('change', function() {
                localStorage.setItem('supabase_key', this.value);
            });
        };
    </script>
</body>
</html>

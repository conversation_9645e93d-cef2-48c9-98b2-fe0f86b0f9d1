# Chatbot Creator - AI-Powered Customer Support SaaS

A modern SaaS web application that allows subscribers to create and deploy their own AI-powered customer support chatbots.

## Features

- **Homepage** with clear CTA and visual previews
- **User Authentication** (signup/login with email/password)
- **Dashboard** with chatbot management and analytics
- **5-Step Chatbot Builder**:
  1. Choose template (Support Bot, Lead Gen Bot, Sales Funnel Bot)
  2. Upload content (PDF, Docs, Text, URLs)
  3. AI training on uploaded content
  4. Customize appearance (name, avatar, colors, greeting)
  5. Generate embeddable JS widget
- **Real-time Preview & Testing**
- **Account Settings** with profile and billing management
- **Multi-chatbot Management** with conversation monitoring

## Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS, shadcn/ui, Vite
- **Backend**: Supabase (auth, database, storage)
- **Authentication**: Email/password via Supabase Auth
- **UI Components**: Radix UI primitives with shadcn/ui
- **Icons**: Lucide React
- **Routing**: React Router DOM

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd chatbot-vs
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Edit `.env` and add your Supabase credentials:
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Start the development server:
```bash
npm run dev
```

### Supabase Setup

1. Create a new Supabase project
2. Run the following SQL to create the database schema:

```sql
-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  plan_type TEXT DEFAULT 'free' CHECK (plan_type IN ('free', 'pro', 'enterprise')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chatbots table
CREATE TABLE public.chatbots (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  template_type TEXT NOT NULL CHECK (template_type IN ('support', 'lead_gen', 'sales_funnel')),
  appearance_settings JSONB DEFAULT '{}',
  embed_code TEXT,
  message_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Uploads table
CREATE TABLE public.uploads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  chatbot_id UUID REFERENCES public.chatbots(id) ON DELETE CASCADE,
  file_url TEXT NOT NULL,
  file_type TEXT NOT NULL,
  processed_status TEXT DEFAULT 'pending' CHECK (processed_status IN ('pending', 'processing', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversations table
CREATE TABLE public.conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  chatbot_id UUID REFERENCES public.chatbots(id) ON DELETE CASCADE,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_input TEXT NOT NULL,
  ai_response TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chatbots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own chatbots" ON public.chatbots
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own chatbots" ON public.chatbots
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own chatbots" ON public.chatbots
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own chatbots" ON public.chatbots
  FOR DELETE USING (auth.uid() = user_id);
```

3. Configure authentication settings in Supabase dashboard
4. Update the environment variables with your Supabase URL and anon key

## Project Structure

```
src/
├── components/
│   ├── ui/                 # shadcn/ui components
│   └── ProtectedRoute.tsx  # Route protection
├── contexts/
│   └── AuthContext.tsx     # Authentication context
├── hooks/
│   └── use-toast.ts        # Toast notifications
├── lib/
│   ├── supabase.ts         # Supabase client and types
│   └── utils.ts            # Utility functions
├── pages/
│   ├── HomePage.tsx        # Landing page
│   ├── LoginPage.tsx       # User login
│   ├── SignupPage.tsx      # User registration
│   ├── Dashboard.tsx       # Main dashboard
│   ├── CreateChatbot.tsx   # Chatbot creation flow
│   ├── ChatbotPreview.tsx  # Preview and testing
│   └── AccountSettings.tsx # User settings
├── App.tsx                 # Main app component
├── main.tsx               # App entry point
└── index.css              # Global styles
```

## Development Phases

### Phase 1: MVP (Current)
- ✅ Project setup and infrastructure
- ✅ Authentication system
- ✅ Basic UI components and pages
- 🔄 Supabase backend integration
- 🔄 Chatbot creation flow
- 🔄 Preview and testing functionality

### Phase 2: AI Integration
- OpenAI/Gemini/DeepSeek API integration
- Content processing and training
- Real-time chat functionality

### Phase 3: Advanced Features
- Billing system (Stripe integration)
- Analytics dashboard
- Multi-chatbot management
- Embed widget generation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.

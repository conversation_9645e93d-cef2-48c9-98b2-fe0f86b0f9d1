-- Fix RLS Configuration for Chatbot Creator
-- Run this in Supabase SQL Editor to fix RLS issues

-- First, drop all existing policies to start fresh
DO $$ 
DECLARE 
    r RECORD;
BEGIN
    -- Drop all existing policies on our tables
    FOR r IN (
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename IN ('users', 'chatbots', 'conversations', 'messages', 'analytics', 'knowledge_base', 'uploads', 'subscriptions')
    ) LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', r.policyname, r.schemaname, r.tablename);
    END LOOP;
END $$;

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chatbots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.knowledge_base ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "users_select_own" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "users_insert_own" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "users_update_own" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Chatbots table policies
CREATE POLICY "chatbots_select_own" ON public.chatbots
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "chatbots_insert_own" ON public.chatbots
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "chatbots_update_own" ON public.chatbots
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "chatbots_delete_own" ON public.chatbots
  FOR DELETE USING (auth.uid() = user_id);

-- Knowledge base policies
CREATE POLICY "knowledge_base_select_own" ON public.knowledge_base
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = knowledge_base.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "knowledge_base_insert_own" ON public.knowledge_base
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = knowledge_base.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "knowledge_base_update_own" ON public.knowledge_base
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = knowledge_base.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "knowledge_base_delete_own" ON public.knowledge_base
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = knowledge_base.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Uploads policies
CREATE POLICY "uploads_select_own" ON public.uploads
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = uploads.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "uploads_insert_own" ON public.uploads
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = uploads.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "uploads_update_own" ON public.uploads
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = uploads.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "uploads_delete_own" ON public.uploads
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = uploads.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Conversations policies (allow public access for website visitors)
CREATE POLICY "conversations_select_own" ON public.conversations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = conversations.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "conversations_insert_public" ON public.conversations
  FOR INSERT WITH CHECK (true); -- Allow anyone to create conversations

CREATE POLICY "conversations_update_own" ON public.conversations
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = conversations.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Messages policies (allow public access for website visitors)
CREATE POLICY "messages_select_own" ON public.messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = messages.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "messages_insert_public" ON public.messages
  FOR INSERT WITH CHECK (true); -- Allow anyone to create messages

-- Analytics policies
CREATE POLICY "analytics_select_own" ON public.analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = analytics.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "analytics_insert_own" ON public.analytics
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = analytics.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "analytics_update_own" ON public.analytics
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = analytics.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Subscriptions policies
CREATE POLICY "subscriptions_select_own" ON public.subscriptions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "subscriptions_insert_own" ON public.subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "subscriptions_update_own" ON public.subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

-- Test the RLS configuration
SELECT 
  'RLS Fix Complete' as status,
  'All policies recreated' as message;

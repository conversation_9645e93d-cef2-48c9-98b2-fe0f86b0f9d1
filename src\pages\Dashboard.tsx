import { Link } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Bot, Plus, MessageCircle, Users, BarChart3 } from 'lucide-react'

export default function Dashboard() {
  const { signOut } = useAuth()

  // Mock data - will be replaced with real data from Supabase
  const chatbots = [
    {
      id: '1',
      name: 'Customer Support Bot',
      template_type: 'support',
      message_count: 127,
      conversations: 45,
      created_at: '2024-01-15'
    },
    {
      id: '2',
      name: 'Lead Generation Bot',
      template_type: 'lead_gen',
      message_count: 89,
      conversations: 23,
      created_at: '2024-01-20'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Bot className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">Chatbot Creator</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/settings">
                <Button variant="ghost">Settings</Button>
              </Link>
              <Button variant="ghost" onClick={signOut}>
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage your AI-powered chatbots</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Chatbots</CardTitle>
              <Bot className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{chatbots.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
              <MessageCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {chatbots.reduce((sum, bot) => sum + bot.message_count, 0)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {chatbots.reduce((sum, bot) => sum + bot.conversations, 0)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chatbots Section */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Your Chatbots</h2>
          <Link to="/create-chatbot">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create New Chatbot
            </Button>
          </Link>
        </div>

        {chatbots.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Bot className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No chatbots yet</h3>
              <p className="text-gray-600 text-center mb-6">
                Create your first AI-powered chatbot to get started
              </p>
              <Link to="/create-chatbot">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Chatbot
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {chatbots.map((chatbot) => (
              <Card key={chatbot.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{chatbot.name}</span>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {chatbot.template_type.replace('_', ' ')}
                    </span>
                  </CardTitle>
                  <CardDescription>
                    Created on {new Date(chatbot.created_at).toLocaleDateString()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Messages:</span>
                      <span className="font-medium">{chatbot.message_count}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Conversations:</span>
                      <span className="font-medium">{chatbot.conversations}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Link to={`/chatbot/${chatbot.id}/preview`} className="flex-1">
                      <Button variant="outline" size="sm" className="w-full">
                        Preview
                      </Button>
                    </Link>
                    <Button variant="outline" size="sm">
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <BarChart3 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

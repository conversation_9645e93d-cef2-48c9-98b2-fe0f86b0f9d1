# 🚀 Complete Supabase Backend Setup

This document provides step-by-step instructions to set up the complete Supabase backend for the Chatbot Creator SaaS application.

## 📋 Prerequisites

- [Supabase account](https://supabase.com) (free tier is sufficient for development)
- Basic understanding of SQL and database concepts
- Node.js 18+ installed locally

## 🏗️ Architecture Overview

The backend consists of:
- **Authentication**: Supabase Auth with email/password
- **Database**: PostgreSQL with Row Level Security (RLS)
- **Storage**: File uploads for training content
- **Real-time**: Live conversation updates
- **API**: Auto-generated REST and GraphQL APIs

## 📊 Database Schema

### Core Tables
- `users` - Extended user profiles with billing info
- `chatbots` - Chatbot configurations and settings
- `knowledge_base` - Training content and knowledge
- `conversations` - Chat sessions with visitors
- `messages` - Individual chat messages
- `analytics` - Usage metrics and performance data
- `uploads` - File upload tracking
- `subscriptions` - Billing and plan management

## 🔧 Step-by-Step Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click **"New Project"**
3. Fill in project details:
   - **Name**: `chatbot-creator`
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your users
4. Click **"Create new project"**
5. Wait 2-3 minutes for project creation

### 2. Configure Authentication

Navigate to **Authentication > Settings**:

#### Site URL Configuration
```
Development: http://localhost:5173
Production: https://your-domain.com
```

#### Email Settings
- ✅ Enable email authentication
- ✅ Enable email confirmations (recommended)
- ✅ Disable email change confirmations (optional)

#### Security Settings
- Set session timeout to 24 hours
- Enable refresh token rotation
- Configure password requirements

### 3. Set Up Database Schema

1. Go to **SQL Editor** in Supabase dashboard
2. Create a new query
3. Copy and paste the entire contents of `supabase/schema.sql`
4. Click **"Run"** to execute

This creates:
- All necessary tables with proper relationships
- Indexes for optimal performance
- Triggers for automatic updates
- Functions for business logic

### 4. Configure Row Level Security

1. In **SQL Editor**, create another new query
2. Copy and paste contents of `supabase/rls-policies.sql`
3. Click **"Run"** to execute

This sets up:
- Security policies ensuring data isolation
- Public access for chatbot conversations
- Automatic user profile creation
- Message counting and quota tracking

### 5. Set Up File Storage

1. Go to **Storage** in Supabase dashboard
2. Create a new bucket: `chatbot-uploads`
3. Set bucket to **Public** for file access
4. Configure storage policies:

```sql
-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload files" ON storage.objects
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow users to view files for their own chatbots
CREATE POLICY "Users can view own chatbot files" ON storage.objects
  FOR SELECT USING (auth.uid()::text = (storage.foldername(name))[1]);

-- Allow users to delete their own files
CREATE POLICY "Users can delete own files" ON storage.objects
  FOR DELETE USING (auth.uid()::text = (storage.foldername(name))[1]);
```

### 6. Add Sample Data (Optional)

For development and testing:

1. Create a test user by signing up through your app
2. Go to **Authentication > Users** and copy the user ID
3. In **SQL Editor**, open `supabase/seed-data.sql`
4. Replace `'YOUR_USER_ID_HERE'` with the actual user ID
5. Run the query to insert sample data

### 7. Configure Environment Variables

1. Go to **Settings > API** in Supabase dashboard
2. Copy your credentials:
   - **Project URL**
   - **Project API Key** (anon/public)

3. Update your `.env` file:
```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

## 🔐 Security Features

### Row Level Security (RLS)
- Users can only access their own data
- Public access for chatbot conversations
- Secure file upload permissions

### Authentication
- Email/password authentication
- Automatic user profile creation
- Session management with refresh tokens

### Data Validation
- Database constraints and checks
- Type safety with TypeScript
- Input validation on frontend

## 📈 Performance Optimizations

### Database Indexes
```sql
-- Key indexes for performance
CREATE INDEX idx_chatbots_user_id ON chatbots(user_id);
CREATE INDEX idx_conversations_chatbot_id ON conversations(chatbot_id);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_analytics_chatbot_date ON analytics(chatbot_id, date);
```

### Query Optimization
- Efficient joins with proper foreign keys
- Pagination for large datasets
- Selective column fetching

## 🧪 Testing the Setup

### 1. Test Authentication
```bash
# Start your React app
npm run dev

# Try these actions:
# - Sign up for a new account
# - Log in with existing credentials
# - Navigate to dashboard
```

### 2. Test Database Operations
```sql
-- Check if user profile was created
SELECT * FROM public.users WHERE email = '<EMAIL>';

-- Verify RLS is working
SELECT * FROM public.chatbots; -- Should only show user's own chatbots
```

### 3. Test File Upload
- Try uploading a file in the chatbot creation flow
- Check if file appears in Storage bucket
- Verify upload record in `uploads` table

## 🔍 Monitoring and Debugging

### Useful Queries
```sql
-- Check message quotas
SELECT 
  email, 
  messages_used, 
  message_quota,
  (message_quota - messages_used) as remaining
FROM users;

-- View chatbot performance
SELECT 
  c.name,
  c.message_count,
  c.conversation_count,
  c.training_status
FROM chatbots c
WHERE c.user_id = 'user-id-here';

-- Analytics summary
SELECT 
  date,
  SUM(total_conversations) as conversations,
  SUM(total_messages) as messages,
  SUM(leads_captured) as leads
FROM analytics
WHERE chatbot_id = 'chatbot-id-here'
GROUP BY date
ORDER BY date DESC;
```

### Common Issues

1. **RLS Policies Too Restrictive**
   - Check policy conditions
   - Verify user authentication
   - Test with `auth.uid()`

2. **File Upload Failures**
   - Check storage bucket permissions
   - Verify file size limits
   - Ensure proper CORS settings

3. **Performance Issues**
   - Monitor query execution time
   - Check index usage
   - Optimize complex queries

## 🚀 Next Steps

After completing the Supabase setup:

1. ✅ **Test Authentication** - Sign up/login functionality
2. ✅ **Test Database** - Create/read/update operations
3. ✅ **Test File Upload** - Upload and process files
4. 🔄 **Integrate AI APIs** - Connect OpenAI/Gemini for responses
5. 🔄 **Implement Real-time** - Live chat functionality
6. 🔄 **Add Billing** - Stripe integration for subscriptions

## 📞 Support

If you encounter issues:

1. **Check Supabase Logs**: Dashboard > Logs
2. **Verify Environment Variables**: Ensure correct URL and keys
3. **Test RLS Policies**: Use SQL Editor to test queries
4. **Check Browser Console**: Look for JavaScript errors
5. **Review Documentation**: [Supabase Docs](https://supabase.com/docs)

## 🎯 Production Checklist

Before going live:

- [ ] Set up production Supabase project
- [ ] Configure custom domain
- [ ] Set up database backups
- [ ] Configure monitoring and alerts
- [ ] Review and test all RLS policies
- [ ] Set up proper CORS settings
- [ ] Configure rate limiting
- [ ] Set up SSL certificates
- [ ] Test disaster recovery procedures

---

**🎉 Congratulations!** Your Supabase backend is now ready for the Chatbot Creator SaaS application!

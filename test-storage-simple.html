<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Storage Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        input { margin: 5px; padding: 8px; width: 300px; }
        button { margin: 5px; padding: 10px 20px; }
    </style>
</head>
<body>
    <h1>🧪 Simple Storage Test</h1>
    
    <div>
        <input type="text" id="supabaseUrl" placeholder="Supabase URL" value="https://your-project.supabase.co">
        <br>
        <input type="text" id="supabaseKey" placeholder="Supabase Anon Key" value="your-anon-key">
        <br>
        <button onclick="testStorage()">Test Storage</button>
        <button onclick="createBucket()">Create Bucket</button>
    </div>

    <div id="results"></div>

    <script>
        let supabase;

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testStorage() {
            clearResults();
            
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('supabaseKey').value;

            if (!url || !key) {
                addResult('❌ Please enter URL and key', 'error');
                return;
            }

            try {
                supabase = window.supabase.createClient(url, key);
                addResult('✅ Supabase client created', 'success');

                // Test 1: List all buckets
                addResult('🔄 Testing storage.listBuckets()...', 'info');
                const { data: buckets, error } = await supabase.storage.listBuckets();
                
                if (error) {
                    addResult(`❌ Storage API Error: ${error.message}`, 'error');
                    addResult(`Error details: ${JSON.stringify(error)}`, 'info');
                } else {
                    addResult(`✅ Storage API works! Found ${buckets.length} buckets`, 'success');
                    if (buckets.length > 0) {
                        buckets.forEach(bucket => {
                            addResult(`   - ${bucket.name} (public: ${bucket.public})`, 'info');
                        });
                    } else {
                        addResult('⚠️ No buckets found', 'warning');
                    }
                }

                // Test 2: Try to access chatbot-uploads bucket
                addResult('🔄 Testing direct bucket access...', 'info');
                try {
                    const { data: files, error: filesError } = await supabase.storage
                        .from('chatbot-uploads')
                        .list('', { limit: 1 });
                    
                    if (filesError) {
                        addResult(`❌ Bucket access failed: ${filesError.message}`, 'error');
                    } else {
                        addResult(`✅ Bucket accessible! Found ${files.length} files`, 'success');
                    }
                } catch (bucketError) {
                    addResult(`❌ Bucket test error: ${bucketError.message}`, 'error');
                }

            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        async function createBucket() {
            if (!supabase) {
                addResult('❌ Initialize storage test first', 'error');
                return;
            }

            addResult('🔄 Creating bucket via API...', 'info');
            
            try {
                const { data, error } = await supabase.storage.createBucket('chatbot-uploads', {
                    public: true,
                    fileSizeLimit: 52428800,
                    allowedMimeTypes: ['image/*', 'application/pdf', 'text/*']
                });

                if (error) {
                    addResult(`❌ Bucket creation failed: ${error.message}`, 'error');
                } else {
                    addResult('✅ Bucket created successfully!', 'success');
                    addResult('🔄 Re-testing storage...', 'info');
                    setTimeout(testStorage, 1000);
                }
            } catch (error) {
                addResult(`❌ Bucket creation error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>

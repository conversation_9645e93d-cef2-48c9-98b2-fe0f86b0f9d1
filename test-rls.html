<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RLS Test - Chatbot Creator</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold mb-6">🔐 Row Level Security Test</h1>
            
            <!-- Status -->
            <div id="status" class="mb-4 p-3 rounded-md bg-blue-100 text-blue-800">
                <p class="text-sm">Ready to test RLS configuration...</p>
            </div>

            <!-- Test Buttons -->
            <div class="grid md:grid-cols-2 gap-4 mb-6">
                <button onclick="testUnauthenticatedAccess()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                    🚫 Test Unauthenticated Access
                </button>
                <button onclick="testPublicAccess()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    🌐 Test Public Access
                </button>
                <button onclick="testAuthenticatedAccess()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    🔑 Test Authenticated Access
                </button>
                <button onclick="runAllRLSTests()" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    🧪 Run All RLS Tests
                </button>
            </div>

            <!-- Authentication Section -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <h3 class="font-semibold mb-3">Quick Authentication (for testing)</h3>
                <div class="grid md:grid-cols-3 gap-2">
                    <input type="email" id="email" placeholder="<EMAIL>" class="p-2 border rounded-md">
                    <input type="password" id="password" placeholder="password123" class="p-2 border rounded-md">
                    <button onclick="quickSignIn()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        Sign In
                    </button>
                </div>
                <div id="authStatus" class="mt-2 text-sm text-gray-600"></div>
            </div>

            <!-- Results -->
            <div id="results" class="space-y-2"></div>
        </div>
    </div>

    <script>
        // Your Supabase credentials
        const SUPABASE_URL = 'https://fabdjjfrjwiqkcyjkyvy.supabase.co'
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZhYmRqamZyandpcWtjeWpreXZ5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2OTU4MzMsImV4cCI6MjA2NjI3MTgzM30.yT3Y9XgTCb4zgPJl0NOfx3pEe6lI3HLw37FM6aKqADo'

        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
        let currentUser = null

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status')
            const colors = {
                info: 'bg-blue-100 text-blue-800',
                success: 'bg-green-100 text-green-800',
                error: 'bg-red-100 text-red-800'
            }
            status.className = `mb-4 p-3 rounded-md ${colors[type]}`
            status.innerHTML = `<p class="text-sm">${message}</p>`
        }

        function addResult(title, message, type = 'info') {
            const colors = {
                success: 'bg-green-50 border-green-200 text-green-800',
                error: 'bg-red-50 border-red-200 text-red-800',
                warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
                info: 'bg-blue-50 border-blue-200 text-blue-800'
            }

            const resultDiv = document.createElement('div')
            resultDiv.className = `border rounded-lg p-3 ${colors[type]}`
            resultDiv.innerHTML = `<strong>${title}:</strong> ${message}`
            
            document.getElementById('results').appendChild(resultDiv)
        }

        async function quickSignIn() {
            const email = document.getElementById('email').value
            const password = document.getElementById('password').value

            if (!email || !password) {
                document.getElementById('authStatus').textContent = 'Please enter email and password'
                return
            }

            try {
                const { data, error } = await supabase.auth.signInWithPassword({ email, password })
                
                if (error) {
                    document.getElementById('authStatus').textContent = `Sign in failed: ${error.message}`
                } else {
                    currentUser = data.user
                    document.getElementById('authStatus').textContent = `Signed in as: ${data.user.email}`
                }
            } catch (error) {
                document.getElementById('authStatus').textContent = `Error: ${error.message}`
            }
        }

        async function testUnauthenticatedAccess() {
            updateStatus('Testing unauthenticated access (should be blocked)...', 'info')
            
            // Sign out first to ensure we're unauthenticated
            await supabase.auth.signOut()
            currentUser = null
            
            try {
                // Test 1: Try to access chatbots (should be blocked)
                const { data: chatbots, error: chatbotsError } = await supabase
                    .from('chatbots')
                    .select('*')

                if (chatbotsError) {
                    addResult('Chatbots Access', `✅ Blocked: ${chatbotsError.message}`, 'success')
                } else if (!chatbots || chatbots.length === 0) {
                    addResult('Chatbots Access', '✅ Blocked: No data returned', 'success')
                } else {
                    addResult('Chatbots Access', `❌ NOT BLOCKED: ${chatbots.length} records returned`, 'error')
                }

                // Test 2: Try to access users (should be blocked)
                const { data: users, error: usersError } = await supabase
                    .from('users')
                    .select('*')

                if (usersError) {
                    addResult('Users Access', `✅ Blocked: ${usersError.message}`, 'success')
                } else if (!users || users.length === 0) {
                    addResult('Users Access', '✅ Blocked: No data returned', 'success')
                } else {
                    addResult('Users Access', `❌ NOT BLOCKED: ${users.length} records returned`, 'error')
                }

                updateStatus('Unauthenticated access test completed', 'success')

            } catch (error) {
                addResult('Unauthenticated Test', `Error: ${error.message}`, 'error')
            }
        }

        async function testPublicAccess() {
            updateStatus('Testing public access (should work for conversations)...', 'info')
            
            try {
                // Test public access to conversations (should work)
                const { data: conversations, error: conversationsError } = await supabase
                    .from('conversations')
                    .select('id, session_id')
                    .limit(5)

                if (conversationsError) {
                    addResult('Conversations Access', `❌ Blocked: ${conversationsError.message}`, 'error')
                } else {
                    addResult('Conversations Access', `✅ Public access working: ${conversations.length} records`, 'success')
                }

                // Test public access to messages (should work)
                const { data: messages, error: messagesError } = await supabase
                    .from('messages')
                    .select('id, content')
                    .limit(5)

                if (messagesError) {
                    addResult('Messages Access', `❌ Blocked: ${messagesError.message}`, 'error')
                } else {
                    addResult('Messages Access', `✅ Public access working: ${messages.length} records`, 'success')
                }

                updateStatus('Public access test completed', 'success')

            } catch (error) {
                addResult('Public Access Test', `Error: ${error.message}`, 'error')
            }
        }

        async function testAuthenticatedAccess() {
            if (!currentUser) {
                updateStatus('Please sign in first to test authenticated access', 'error')
                return
            }

            updateStatus('Testing authenticated access (should work for own data)...', 'info')
            
            try {
                // Test access to own user profile
                const { data: profile, error: profileError } = await supabase
                    .from('users')
                    .select('*')
                    .eq('id', currentUser.id)

                if (profileError) {
                    addResult('User Profile', `❌ Error: ${profileError.message}`, 'error')
                } else if (profile && profile.length > 0) {
                    addResult('User Profile', `✅ Access granted: ${profile[0].email}`, 'success')
                } else {
                    addResult('User Profile', '⚠️ No profile found', 'warning')
                }

                // Test access to own chatbots
                const { data: chatbots, error: chatbotsError } = await supabase
                    .from('chatbots')
                    .select('*')
                    .eq('user_id', currentUser.id)

                if (chatbotsError) {
                    addResult('Own Chatbots', `❌ Error: ${chatbotsError.message}`, 'error')
                } else {
                    addResult('Own Chatbots', `✅ Access granted: ${chatbots.length} chatbots`, 'success')
                }

                updateStatus('Authenticated access test completed', 'success')

            } catch (error) {
                addResult('Authenticated Test', `Error: ${error.message}`, 'error')
            }
        }

        async function runAllRLSTests() {
            document.getElementById('results').innerHTML = ''
            
            addResult('RLS Test Suite', 'Starting comprehensive RLS tests...', 'info')
            
            await testUnauthenticatedAccess()
            await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second
            
            await testPublicAccess()
            await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second
            
            if (currentUser) {
                await testAuthenticatedAccess()
            } else {
                addResult('Authenticated Tests', 'Skipped - no user signed in', 'warning')
            }
            
            addResult('RLS Test Suite', '🎉 All RLS tests completed!', 'success')
        }

        // Check initial auth state
        supabase.auth.getUser().then(({ data: { user } }) => {
            if (user) {
                currentUser = user
                document.getElementById('authStatus').textContent = `Already signed in as: ${user.email}`
            }
        })
    </script>
</body>
</html>

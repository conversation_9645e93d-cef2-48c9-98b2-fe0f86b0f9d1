-- Create storage bucket without policies (for testing)
-- Run this in Supabase SQL Editor

-- Create the bucket with public access
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'chatbot-uploads', 
  'chatbot-uploads', 
  true, 
  52428800, -- 50MB limit
  ARRAY[
    'image/*',
    'application/pdf',
    'text/*',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Verify the bucket was created
SELECT 
  'Bucket Status' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE name = 'chatbot-uploads') 
    THEN '✅ Bucket created successfully'
    ELSE '❌ Bucket creation failed'
  END as result;

-- Show bucket details
SELECT 
  name,
  public,
  file_size_limit / 1024 / 1024 as size_limit_mb,
  array_length(allowed_mime_types, 1) as allowed_types_count,
  created_at
FROM storage.buckets 
WHERE name = 'chatbot-uploads';

-- Seed data for Chatbot Creator
-- Run this SQL after setting up schema and RLS policies
-- This creates sample data for testing and development

-- Note: You'll need to replace the user IDs with actual user IDs from your auth.users table
-- You can get these by signing up test users first, then running:
-- SELECT id, email FROM auth.users;

-- Sample chatbot templates data
INSERT INTO public.chatbots (id, user_id, name, template_type, appearance_settings, training_status, embed_code, is_active, message_count, conversation_count) VALUES
-- Replace 'YOUR_USER_ID_HERE' with actual user ID from auth.users
('550e8400-e29b-41d4-a716-************', '99d2da93-5f1f-4891-8b12-f1353bf60609', 'Customer Support Bot', 'support', '{
  "primaryColor": "#2563eb",
  "greeting": "Hi! How can I help you with your order or account today?",
  "avatar": "support",
  "position": "bottom-right",
  "welcomeMessage": "Welcome to our support center!"
}', 'completed', '<script src="https://chatbot-creator.com/embed.js" data-chatbot-id="550e8400-e29b-41d4-a716-************"></script>', true, 127, 45),

('550e8400-e29b-41d4-a716-************', '99d2da93-5f1f-4891-8b12-f1353bf60609', 'Lead Generation Bot', 'lead_gen', '{
  "primaryColor": "#059669",
  "greeting": "Hi there! Interested in learning more about our services?",
  "avatar": "sales",
  "position": "bottom-right",
  "welcomeMessage": "Let me help you find the perfect solution!"
}', 'completed', '<script src="https://chatbot-creator.com/embed.js" data-chatbot-id="550e8400-e29b-41d4-a716-************"></script>', true, 89, 23),

('550e8400-e29b-41d4-a716-************', 'YUR_USER_ID_HERE', 'Sales Funnel Bot', 'sales_funnel', '{
  "primaryColor": "#dc2626",
  "greeting": "Ready to get started? I can help you choose the right plan!",
  "avatar": "consultant",
  "position": "bottom-right",
  "welcomeMessage": "Let me guide you through our offerings!"
}', 'training', '<script src="https://chatbot-creator.com/embed.js" data-chatbot-id="550e8400-e29b-41d4-a716-************"></script>', false, 0, 0);

-- Sample knowledge base content
INSERT INTO public.knowledge_base (chatbot_id, content_type, title, content, source_url) VALUES
('550e8400-e29b-41d4-a716-************', 'text', 'Business Hours', 'Our customer support is available Monday through Friday, 9 AM to 6 PM EST. For urgent issues outside business hours, <NAME_EMAIL> and we will respond within 24 hours.', null),

('550e8400-e29b-41d4-a716-************', 'text', 'Return Policy', 'We offer a 30-day return policy for all products. Items must be in original condition with tags attached. To initiate a return, please contact our support team with your order number.', null),

('550e8400-e29b-41d4-a716-************', 'text', 'Shipping Information', 'We offer free shipping on orders over $50. Standard shipping takes 3-5 business days, while express shipping takes 1-2 business days. International shipping is available to most countries.', null),

('550e8400-e29b-41d4-a716-************', 'text', 'Service Pricing', 'Our basic plan starts at $29/month and includes up to 5 chatbots and 1,000 messages. Our pro plan is $99/month with unlimited chatbots and 10,000 messages. Enterprise plans are available for larger organizations.', null),

('550e8400-e29b-41d4-a716-************', 'text', 'Features Overview', 'Our platform includes AI-powered chatbots, real-time analytics, custom branding, multi-language support, and integrations with popular CRM systems like Salesforce and HubSpot.', null);

-- Sample conversations
INSERT INTO public.conversations (id, chatbot_id, session_id, visitor_id, visitor_email, visitor_name, started_at, ended_at, message_count, lead_captured) VALUES
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'session_001', 'visitor_001', '<EMAIL>', 'John Doe', NOW() - INTERVAL '2 hours', NOW() - INTERVAL '1 hour 45 minutes', 8, false),

('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'session_002', 'visitor_002', null, null, NOW() - INTERVAL '1 hour', NOW() - INTERVAL '45 minutes', 5, false),

('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'session_003', 'visitor_003', '<EMAIL>', 'Sarah Smith', NOW() - INTERVAL '30 minutes', null, 12, true);

-- Sample messages
INSERT INTO public.messages (conversation_id, chatbot_id, content, sender_type, timestamp) VALUES
-- Conversation 1
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Hi! How can I help you with your order or account today?', 'bot', NOW() - INTERVAL '2 hours'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'What are your business hours?', 'user', NOW() - INTERVAL '2 hours' + INTERVAL '1 minute'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Our customer support is available Monday through Friday, 9 AM to 6 PM EST. For urgent issues outside business hours, <NAME_EMAIL> and we will respond within 24 hours.', 'bot', NOW() - INTERVAL '2 hours' + INTERVAL '2 minutes'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Great, thank you! Can you tell me about your return policy?', 'user', NOW() - INTERVAL '2 hours' + INTERVAL '3 minutes'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'We offer a 30-day return policy for all products. Items must be in original condition with tags attached. To initiate a return, please contact our support team with your order number.', 'bot', NOW() - INTERVAL '2 hours' + INTERVAL '4 minutes'),

-- Conversation 2
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Hi! How can I help you with your order or account today?', 'bot', NOW() - INTERVAL '1 hour'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Do you offer free shipping?', 'user', NOW() - INTERVAL '1 hour' + INTERVAL '30 seconds'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'We offer free shipping on orders over $50. Standard shipping takes 3-5 business days, while express shipping takes 1-2 business days.', 'bot', NOW() - INTERVAL '1 hour' + INTERVAL '1 minute'),

-- Conversation 3 (Lead Gen)
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Hi there! Interested in learning more about our services?', 'bot', NOW() - INTERVAL '30 minutes'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Yes, I am looking for a chatbot solution for my business', 'user', NOW() - INTERVAL '30 minutes' + INTERVAL '1 minute'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Perfect! Our platform includes AI-powered chatbots, real-time analytics, custom branding, and CRM integrations. What size is your business?', 'bot', NOW() - INTERVAL '30 minutes' + INTERVAL '2 minutes'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'We are a medium-sized company with about 100 employees', 'user', NOW() - INTERVAL '30 minutes' + INTERVAL '3 minutes'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Great! Our Pro plan would be perfect for your needs. It includes unlimited chatbots and 10,000 messages for $99/month. Would you like me to connect you with our sales team?', 'bot', NOW() - INTERVAL '30 minutes' + INTERVAL '4 minutes'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Yes, please! My <NAME_EMAIL>', 'user', NOW() - INTERVAL '30 minutes' + INTERVAL '5 minutes');

-- Sample analytics data
INSERT INTO public.analytics (chatbot_id, date, total_conversations, total_messages, unique_visitors, leads_captured, avg_conversation_length) VALUES
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '7 days', 12, 48, 12, 0, 4.0),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '6 days', 15, 62, 15, 0, 4.1),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '5 days', 8, 29, 8, 0, 3.6),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '4 days', 18, 75, 18, 0, 4.2),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '3 days', 22, 95, 22, 0, 4.3),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '2 days', 16, 68, 16, 0, 4.3),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '1 day', 19, 82, 19, 0, 4.3),

('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '7 days', 5, 35, 5, 2, 7.0),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '6 days', 8, 56, 8, 3, 7.0),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '5 days', 3, 21, 3, 1, 7.0),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '4 days', 7, 49, 7, 2, 7.0),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '3 days', 9, 63, 9, 4, 7.0),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '2 days', 6, 42, 6, 2, 7.0),
('550e8400-e29b-41d4-a716-************', CURRENT_DATE - INTERVAL '1 day', 4, 28, 4, 1, 7.0);

-- Sample file uploads
INSERT INTO public.uploads (chatbot_id, file_name, file_url, file_type, file_size, processed_status) VALUES
('550e8400-e29b-41d4-a716-************', 'support-documentation.pdf', 'https://example.com/files/support-docs.pdf', 'application/pdf', 2048576, 'completed'),
('550e8400-e29b-41d4-a716-************', 'faq-content.txt', 'https://example.com/files/faq.txt', 'text/plain', 15360, 'completed'),
('550e8400-e29b-41d4-a716-************', 'product-catalog.pdf', 'https://example.com/files/catalog.pdf', 'application/pdf', 5242880, 'completed'),
('550e8400-e29b-41d4-a716-************', 'sales-scripts.docx', 'https://example.com/files/scripts.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 1048576, 'processing');

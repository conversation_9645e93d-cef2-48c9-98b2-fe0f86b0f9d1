-- Simplified Storage Policies for chatbot-uploads bucket
-- Run this in Supabase SQL Editor with elevated permissions

-- First, create the bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('chatbot-uploads', 'chatbot-uploads', true, 52428800, ARRAY['image/*', 'application/pdf', 'text/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
ON CONFLICT (id) DO NOTHING;

-- Simple storage policies (these should work with standard permissions)
-- Note: If these still fail, use the Dashboard method above

-- Allow public read access to all files in the bucket
CREATE POLICY IF NOT EXISTS "Public Access"
ON storage.objects FOR SELECT
USING (bucket_id = 'chatbot-uploads');

-- Allow authenticated users to upload files
CREATE POLICY IF NOT EXISTS "Authenticated Upload"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'chatbot-uploads' AND auth.role() = 'authenticated');

-- Allow authenticated users to delete their own files
CREATE POLICY IF NOT EXISTS "Authenticated Delete Own"
ON storage.objects FOR DELETE
USING (bucket_id = 'chatbot-uploads' AND auth.role() = 'authenticated');

-- Verify the bucket was created
SELECT 
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE name = 'chatbot-uploads';

-- Quick test to verify storage and <PERSON><PERSON> fixes
-- Run this after running the storage and <PERSON><PERSON> fix scripts

-- Test 1: Check storage bucket
SELECT 
  'Storage Test' as test_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE name = 'chatbot-uploads') 
    THEN '✅ Bucket exists'
    ELSE '❌ Bucket missing'
  END as result;

-- Test 2: Check RLS is enabled
SELECT 
  'RLS Test' as test_type,
  tablename,
  CASE 
    WHEN rowsecurity THEN '✅ RLS enabled'
    ELSE '❌ RLS disabled'
  END as result
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'chatbots', 'conversations', 'messages')
ORDER BY tablename;

-- Test 3: Check policies exist
SELECT 
  'Policy Test' as test_type,
  tablename,
  COUNT(*) as policy_count,
  CASE 
    WHEN COUNT(*) > 0 THEN '✅ Policies exist'
    ELSE '❌ No policies'
  END as result
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN ('users', 'chatbots', 'conversations', 'messages')
GROUP BY tablename
ORDER BY tablename;

-- Test 4: Test unauthenticated access (should be blocked for chatbots)
SELECT 
  'Unauthenticated Access Test' as test_type,
  'chatbots' as table_name,
  COUNT(*) as accessible_records,
  CASE 
    WHEN COUNT(*) = 0 THEN '✅ RLS blocking access'
    ELSE '⚠️ RLS might not be working'
  END as result
FROM chatbots;

-- Test 5: Test public access (should work for conversations)
SELECT 
  'Public Access Test' as test_type,
  'conversations' as table_name,
  COUNT(*) as accessible_records,
  CASE 
    WHEN COUNT(*) > 0 THEN '✅ Public access working'
    ELSE '⚠️ No conversations or access blocked'
  END as result
FROM conversations;

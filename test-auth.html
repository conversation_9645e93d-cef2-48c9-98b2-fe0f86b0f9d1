<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Auth Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 class="text-2xl font-bold mb-6 text-center">Supabase Auth Test</h1>
        
        <!-- Status Display -->
        <div id="status" class="mb-4 p-3 rounded-md bg-gray-100">
            <p class="text-sm text-gray-600">Initializing...</p>
        </div>

        <!-- Login Form -->
        <div id="loginForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input type="email" id="email" class="w-full p-2 border border-gray-300 rounded-md" 
                       placeholder="<EMAIL>">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <input type="password" id="password" class="w-full p-2 border border-gray-300 rounded-md" 
                       placeholder="password123">
            </div>
            <div class="flex space-x-2">
                <button onclick="signUp()" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                    Sign Up
                </button>
                <button onclick="signIn()" class="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700">
                    Sign In
                </button>
            </div>
        </div>

        <!-- User Info -->
        <div id="userInfo" class="hidden space-y-4">
            <div class="bg-green-50 p-3 rounded-md">
                <p class="text-sm text-green-800">✅ Authenticated successfully!</p>
            </div>
            <div id="userDetails" class="text-sm text-gray-600"></div>
            <button onclick="signOut()" class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700">
                Sign Out
            </button>
            <button onclick="testDatabase()" class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700">
                Test Database Access
            </button>
        </div>

        <!-- Database Test Results -->
        <div id="dbResults" class="hidden mt-4 p-3 bg-gray-50 rounded-md">
            <h3 class="font-medium mb-2">Database Test Results:</h3>
            <div id="dbContent" class="text-sm text-gray-600"></div>
        </div>
    </div>

    <script>
        // Your actual Supabase credentials
        const SUPABASE_URL = 'https://fabdjjfrjwiqkcyjkyvy.supabase.co'
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZhYmRqamZyandpcWtjeWpreXZ5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2OTU4MzMsImV4cCI6MjA2NjI3MTgzM30.yT3Y9XgTCb4zgPJl0NOfx3pEe6lI3HLw37FM6aKqADo'

        // Initialize Supabase client
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

        let currentUser = null

        // Update status display
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status')
            const colors = {
                info: 'bg-blue-100 text-blue-800',
                success: 'bg-green-100 text-green-800',
                error: 'bg-red-100 text-red-800'
            }
            status.className = `mb-4 p-3 rounded-md ${colors[type]}`
            status.innerHTML = `<p class="text-sm">${message}</p>`
        }

        // Check initial auth state
        async function checkAuthState() {
            try {
                const { data: { user } } = await supabase.auth.getUser()
                if (user) {
                    currentUser = user
                    showUserInfo(user)
                    updateStatus('Already signed in', 'success')
                } else {
                    updateStatus('Not signed in. Please sign up or sign in.', 'info')
                }
            } catch (error) {
                updateStatus(`Error checking auth state: ${error.message}`, 'error')
            }
        }

        // Sign up function
        async function signUp() {
            const email = document.getElementById('email').value
            const password = document.getElementById('password').value

            if (!email || !password) {
                updateStatus('Please enter email and password', 'error')
                return
            }

            try {
                updateStatus('Creating account...', 'info')
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password
                })

                if (error) {
                    updateStatus(`Sign up error: ${error.message}`, 'error')
                } else {
                    updateStatus('Account created! Check your email for verification.', 'success')
                    if (data.user) {
                        currentUser = data.user
                        showUserInfo(data.user)
                    }
                }
            } catch (error) {
                updateStatus(`Sign up failed: ${error.message}`, 'error')
            }
        }

        // Sign in function
        async function signIn() {
            const email = document.getElementById('email').value
            const password = document.getElementById('password').value

            if (!email || !password) {
                updateStatus('Please enter email and password', 'error')
                return
            }

            try {
                updateStatus('Signing in...', 'info')
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                })

                if (error) {
                    updateStatus(`Sign in error: ${error.message}`, 'error')
                } else {
                    updateStatus('Signed in successfully!', 'success')
                    currentUser = data.user
                    showUserInfo(data.user)
                }
            } catch (error) {
                updateStatus(`Sign in failed: ${error.message}`, 'error')
            }
        }

        // Sign out function
        async function signOut() {
            try {
                const { error } = await supabase.auth.signOut()
                if (error) {
                    updateStatus(`Sign out error: ${error.message}`, 'error')
                } else {
                    updateStatus('Signed out successfully', 'success')
                    currentUser = null
                    showLoginForm()
                    document.getElementById('dbResults').classList.add('hidden')
                }
            } catch (error) {
                updateStatus(`Sign out failed: ${error.message}`, 'error')
            }
        }

        // Show user info
        function showUserInfo(user) {
            document.getElementById('loginForm').classList.add('hidden')
            document.getElementById('userInfo').classList.remove('hidden')
            
            document.getElementById('userDetails').innerHTML = `
                <strong>User ID:</strong> ${user.id}<br>
                <strong>Email:</strong> ${user.email}<br>
                <strong>Created:</strong> ${new Date(user.created_at).toLocaleString()}
            `
        }

        // Show login form
        function showLoginForm() {
            document.getElementById('loginForm').classList.remove('hidden')
            document.getElementById('userInfo').classList.add('hidden')
        }

        // Test database access
        async function testDatabase() {
            if (!currentUser) {
                updateStatus('Please sign in first', 'error')
                return
            }

            try {
                updateStatus('Testing database access...', 'info')
                
                // Test 1: Get user profile
                const { data: profile, error: profileError } = await supabase
                    .from('users')
                    .select('*')
                    .eq('id', currentUser.id)
                    .single()

                // Test 2: Get user's chatbots
                const { data: chatbots, error: chatbotsError } = await supabase
                    .from('chatbots')
                    .select('*')
                    .eq('user_id', currentUser.id)

                // Test 3: Get conversations for user's chatbots
                const { data: conversations, error: conversationsError } = await supabase
                    .from('conversations')
                    .select('*')
                    .in('chatbot_id', chatbots?.map(bot => bot.id) || [])

                let results = '<div class="space-y-2">'
                
                if (profileError) {
                    results += `<div class="text-red-600">❌ Profile error: ${profileError.message}</div>`
                } else {
                    results += `<div class="text-green-600">✅ Profile: ${profile?.email || 'Found'}</div>`
                }

                if (chatbotsError) {
                    results += `<div class="text-red-600">❌ Chatbots error: ${chatbotsError.message}</div>`
                } else {
                    results += `<div class="text-green-600">✅ Chatbots: ${chatbots?.length || 0} found</div>`
                    if (chatbots && chatbots.length > 0) {
                        chatbots.forEach(bot => {
                            results += `<div class="ml-4 text-sm text-gray-600">- ${bot.name} (${bot.template_type})</div>`
                        })
                    }
                }

                if (conversationsError) {
                    results += `<div class="text-red-600">❌ Conversations error: ${conversationsError.message}</div>`
                } else {
                    results += `<div class="text-green-600">✅ Conversations: ${conversations?.length || 0} found</div>`
                }

                results += '</div>'

                document.getElementById('dbContent').innerHTML = results
                document.getElementById('dbResults').classList.remove('hidden')
                updateStatus('Database test completed', 'success')

            } catch (error) {
                updateStatus(`Database test failed: ${error.message}`, 'error')
            }
        }

        // Listen for auth changes
        supabase.auth.onAuthStateChange((event, session) => {
            console.log('Auth state changed:', event, session)
            if (event === 'SIGNED_IN') {
                currentUser = session.user
                showUserInfo(session.user)
            } else if (event === 'SIGNED_OUT') {
                currentUser = null
                showLoginForm()
            }
        })

        // Initialize
        checkAuthState()

        // Show instructions if credentials not set
        if (SUPABASE_URL === 'YOUR_SUPABASE_URL_HERE') {
            updateStatus('⚠️ Please update the Supabase credentials in this file before testing', 'error')
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Storage Test - Chatbot Creator</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-4">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold mb-6">🗂️ Storage Test</h1>
            
            <!-- Status -->
            <div id="status" class="mb-4 p-3 rounded-md bg-blue-100 text-blue-800">
                <p class="text-sm">Initializing storage test...</p>
            </div>

            <!-- Bucket Test -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">1. Bucket Configuration Test</h2>
                <button onclick="testBucket()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Test Bucket Access
                </button>
            </div>

            <!-- File Upload Test -->
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-3">2. File Upload Test</h2>
                <div class="space-y-3">
                    <input type="file" id="fileInput" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    <button onclick="uploadFile()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        Upload Test File
                    </button>
                </div>
            </div>

            <!-- Results -->
            <div id="results" class="space-y-2"></div>
        </div>
    </div>

    <script>
        // Your Supabase credentials
        const SUPABASE_URL = 'https://fabdjjfrjwiqkcyjkyvy.supabase.co'
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZhYmRqamZyandpcWtjeWpreXZ5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2OTU4MzMsImV4cCI6MjA2NjI3MTgzM30.yT3Y9XgTCb4zgPJl0NOfx3pEe6lI3HLw37FM6aKqADo'

        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status')
            const colors = {
                info: 'bg-blue-100 text-blue-800',
                success: 'bg-green-100 text-green-800',
                error: 'bg-red-100 text-red-800'
            }
            status.className = `mb-4 p-3 rounded-md ${colors[type]}`
            status.innerHTML = `<p class="text-sm">${message}</p>`
        }

        function addResult(message, type = 'info') {
            const colors = {
                success: 'bg-green-50 border-green-200 text-green-800',
                error: 'bg-red-50 border-red-200 text-red-800',
                info: 'bg-blue-50 border-blue-200 text-blue-800'
            }

            const resultDiv = document.createElement('div')
            resultDiv.className = `border rounded-lg p-3 ${colors[type]}`
            resultDiv.innerHTML = `<p class="text-sm">${message}</p>`
            
            document.getElementById('results').appendChild(resultDiv)
        }

        async function testBucket() {
            updateStatus('Testing storage bucket access...', 'info')
            
            try {
                // Test 1: List buckets
                const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
                
                if (bucketsError) {
                    addResult(`❌ Error accessing storage: ${bucketsError.message}`, 'error')
                    updateStatus('Storage test failed', 'error')
                    return
                }

                addResult(`✅ Storage accessible. Found ${buckets.length} buckets`, 'success')
                
                // Test 2: Check for chatbot-uploads bucket
                const chatbotBucket = buckets.find(bucket => bucket.name === 'chatbot-uploads')
                
                if (chatbotBucket) {
                    addResult('✅ "chatbot-uploads" bucket found', 'success')
                    addResult(`   - Public: ${chatbotBucket.public}`, 'info')
                    addResult(`   - Created: ${new Date(chatbotBucket.created_at).toLocaleString()}`, 'info')
                } else {
                    addResult('❌ "chatbot-uploads" bucket not found', 'error')
                    addResult('Available buckets: ' + buckets.map(b => b.name).join(', '), 'info')
                    updateStatus('Bucket missing - please create it in Supabase dashboard', 'error')
                    return
                }

                // Test 3: Try to list files in bucket
                const { data: files, error: filesError } = await supabase.storage
                    .from('chatbot-uploads')
                    .list('', { limit: 5 })

                if (filesError) {
                    addResult(`⚠️ Cannot list files: ${filesError.message}`, 'error')
                } else {
                    addResult(`✅ Bucket accessible. Contains ${files.length} files`, 'success')
                }

                updateStatus('Bucket test completed successfully', 'success')

            } catch (error) {
                addResult(`❌ Storage test error: ${error.message}`, 'error')
                updateStatus('Storage test failed', 'error')
            }
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput')
            const file = fileInput.files[0]

            if (!file) {
                updateStatus('Please select a file first', 'error')
                return
            }

            updateStatus('Uploading file...', 'info')

            try {
                // Create a test file path
                const fileName = `test/${Date.now()}-${file.name}`

                // Upload file
                const { data: uploadData, error: uploadError } = await supabase.storage
                    .from('chatbot-uploads')
                    .upload(fileName, file)

                if (uploadError) {
                    addResult(`❌ Upload failed: ${uploadError.message}`, 'error')
                    updateStatus('File upload failed', 'error')
                    return
                }

                addResult(`✅ File uploaded successfully: ${uploadData.path}`, 'success')

                // Get public URL
                const { data: { publicUrl } } = supabase.storage
                    .from('chatbot-uploads')
                    .getPublicUrl(fileName)

                addResult(`✅ Public URL generated: ${publicUrl}`, 'success')

                // Test if URL is accessible
                try {
                    const response = await fetch(publicUrl)
                    if (response.ok) {
                        addResult('✅ File is publicly accessible', 'success')
                    } else {
                        addResult(`⚠️ File uploaded but not publicly accessible (${response.status})`, 'error')
                    }
                } catch (fetchError) {
                    addResult(`⚠️ Could not test public access: ${fetchError.message}`, 'error')
                }

                updateStatus('File upload test completed', 'success')

            } catch (error) {
                addResult(`❌ Upload error: ${error.message}`, 'error')
                updateStatus('File upload failed', 'error')
            }
        }

        // Initialize
        updateStatus('Storage test ready. Click "Test Bucket Access" to begin.', 'info')
    </script>
</body>
</html>

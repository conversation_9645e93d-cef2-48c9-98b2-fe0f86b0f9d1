# Supabase Setup Guide for Chatbot Creator

This guide will walk you through setting up the Supabase backend for the Chatbot Creator SaaS application.

## Prerequisites

- Supabase account (sign up at [supabase.com](https://supabase.com))
- Basic understanding of SQL

## Step 1: Create a New Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `chatbot-creator`
   - **Database Password**: Choose a strong password
   - **Region**: Select the region closest to your users
5. Click "Create new project"
6. Wait for the project to be created (usually takes 2-3 minutes)

## Step 2: Configure Authentication

1. Go to **Authentication > Settings** in your Supabase dashboard
2. Configure the following settings:

### Site URL
- Set your site URL to `http://localhost:5173` for development
- For production, use your actual domain

### Auth Providers
- **Email**: Enable email authentication (should be enabled by default)
- **Confirm email**: Enable if you want email verification
- **Enable email confirmations**: Recommended for production

### Email Templates (Optional)
- Customize the email templates for signup confirmation, password reset, etc.

## Step 3: Set Up Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Create a new query
3. Copy and paste the contents of `supabase/schema.sql`
4. Click "Run" to execute the schema creation

This will create all the necessary tables:
- `users` - User profiles and plan information
- `chatbots` - Chatbot configurations and settings
- `knowledge_base` - Training content for chatbots
- `uploads` - File upload tracking
- `conversations` - Chat conversations
- `messages` - Individual chat messages
- `analytics` - Usage analytics and metrics
- `subscriptions` - Billing and subscription data

## Step 4: Set Up Row Level Security (RLS)

1. In the **SQL Editor**, create another new query
2. Copy and paste the contents of `supabase/rls-policies.sql`
3. Click "Run" to execute the RLS policies

This will:
- Enable RLS on all tables
- Create security policies to ensure users can only access their own data
- Set up triggers for automatic user profile creation
- Create functions for message counting and embed code generation

## Step 5: Add Sample Data (Optional)

For testing and development:

1. First, create a test user by signing up through your application
2. Go to **Authentication > Users** and copy the user ID
3. In the **SQL Editor**, create a new query
4. Copy the contents of `supabase/seed-data.sql`
5. Replace `'YOUR_USER_ID_HERE'` with the actual user ID you copied
6. Click "Run" to insert sample data

## Step 6: Configure Storage (For File Uploads)

1. Go to **Storage** in your Supabase dashboard
2. Create a new bucket called `chatbot-uploads`
3. Set the bucket to **Public** if you want files to be publicly accessible
4. Configure RLS policies for the storage bucket:

```sql
-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload files" ON storage.objects
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow users to view files for their own chatbots
CREATE POLICY "Users can view own chatbot files" ON storage.objects
  FOR SELECT USING (auth.uid()::text = (storage.foldername(name))[1]);
```

## Step 7: Get Your Environment Variables

1. Go to **Settings > API** in your Supabase dashboard
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Project API Key** (anon/public key)

3. Update your `.env` file:

```env
VITE_SUPABASE_URL=your_project_url_here
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

## Step 8: Test the Connection

1. Start your React application:
```bash
npm run dev
```

2. Try to:
   - Sign up for a new account
   - Log in with existing credentials
   - Navigate to the dashboard

## Database Schema Overview

### Core Tables

- **users**: Extended user profiles with plan information
- **chatbots**: Chatbot configurations and appearance settings
- **knowledge_base**: Training content and knowledge for chatbots
- **conversations**: Chat sessions between visitors and chatbots
- **messages**: Individual messages within conversations
- **analytics**: Daily analytics and metrics
- **uploads**: File upload tracking and processing status
- **subscriptions**: Billing and subscription management

### Key Features

- **Automatic user profile creation** when users sign up
- **Message counting and quota tracking** for billing
- **Real-time conversation tracking** for analytics
- **Secure file upload handling** with proper permissions
- **Comprehensive analytics** for chatbot performance

## Security Features

- **Row Level Security (RLS)** ensures users can only access their own data
- **Secure authentication** with Supabase Auth
- **API key protection** with proper environment variable usage
- **File upload security** with bucket-level permissions

## Next Steps

After completing the Supabase setup:

1. Test user authentication in your React app
2. Implement chatbot creation functionality
3. Set up file upload handling
4. Integrate AI APIs for chatbot responses
5. Implement billing with Stripe (future phase)

## Troubleshooting

### Common Issues

1. **RLS Policies Too Restrictive**: If you can't access data, check the RLS policies
2. **Environment Variables**: Make sure your `.env` file is properly configured
3. **CORS Issues**: Ensure your site URL is correctly set in Supabase settings
4. **Database Permissions**: Verify that all tables have proper RLS policies enabled

### Useful SQL Queries for Debugging

```sql
-- Check if user profile was created
SELECT * FROM public.users WHERE email = '<EMAIL>';

-- View all chatbots for a user
SELECT * FROM public.chatbots WHERE user_id = 'user-id-here';

-- Check message counts
SELECT 
  c.name,
  c.message_count,
  COUNT(m.id) as actual_messages
FROM public.chatbots c
LEFT JOIN public.messages m ON c.id = m.chatbot_id
GROUP BY c.id, c.name, c.message_count;
```

## Support

If you encounter issues:
1. Check the Supabase logs in the dashboard
2. Verify your RLS policies are correct
3. Ensure environment variables are properly set
4. Check the browser console for any JavaScript errors

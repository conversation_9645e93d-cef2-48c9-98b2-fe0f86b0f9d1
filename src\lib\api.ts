import { supabase } from './supabase'
import type { Database } from './supabase'

type Tables = Database['public']['Tables']
type Chatbot = Tables['chatbots']['Row']
type ChatbotInsert = Tables['chatbots']['Insert']
type ChatbotUpdate = Tables['chatbots']['Update']
type User = Tables['users']['Row']
type Conversation = Tables['conversations']['Row']
type Message = Tables['messages']['Row']
type Analytics = Tables['analytics']['Row']

// User API functions
export const userApi = {
  // Get current user profile
  async getProfile() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()

    if (error) throw error
    return data
  },

  // Update user profile
  async updateProfile(updates: Partial<User>) {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Check if user has reached message quota
  async checkMessageQuota() {
    const profile = await this.getProfile()
    return {
      used: profile.messages_used,
      quota: profile.message_quota,
      remaining: profile.message_quota - profile.messages_used,
      canSendMessage: profile.messages_used < profile.message_quota
    }
  }
}

// Chatbot API functions
export const chatbotApi = {
  // Get all chatbots for current user
  async getAll() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await supabase
      .from('chatbots')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get single chatbot by ID
  async getById(id: string) {
    const { data, error } = await supabase
      .from('chatbots')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  // Create new chatbot
  async create(chatbot: ChatbotInsert) {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await supabase
      .from('chatbots')
      .insert({ ...chatbot, user_id: user.id })
      .select()
      .single()

    if (error) throw error

    // Generate embed code
    const embedCode = `<script src="https://chatbot-creator.com/embed.js" data-chatbot-id="${data.id}"></script>`
    
    // Update with embed code
    const { data: updatedData, error: updateError } = await supabase
      .from('chatbots')
      .update({ embed_code: embedCode })
      .eq('id', data.id)
      .select()
      .single()

    if (updateError) throw updateError
    return updatedData
  },

  // Update chatbot
  async update(id: string, updates: ChatbotUpdate) {
    const { data, error } = await supabase
      .from('chatbots')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete chatbot
  async delete(id: string) {
    const { error } = await supabase
      .from('chatbots')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  // Get chatbot analytics
  async getAnalytics(id: string, days: number = 7) {
    const { data, error } = await supabase
      .from('analytics')
      .select('*')
      .eq('chatbot_id', id)
      .gte('date', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('date', { ascending: true })

    if (error) throw error
    return data
  }
}

// Knowledge base API functions
export const knowledgeBaseApi = {
  // Get knowledge base for chatbot
  async getForChatbot(chatbotId: string) {
    const { data, error } = await supabase
      .from('knowledge_base')
      .select('*')
      .eq('chatbot_id', chatbotId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Add knowledge base entry
  async add(entry: Tables['knowledge_base']['Insert']) {
    const { data, error } = await supabase
      .from('knowledge_base')
      .insert(entry)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete knowledge base entry
  async delete(id: string) {
    const { error } = await supabase
      .from('knowledge_base')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Conversation API functions
export const conversationApi = {
  // Get conversations for chatbot
  async getForChatbot(chatbotId: string, limit: number = 50) {
    const { data, error } = await supabase
      .from('conversations')
      .select('*')
      .eq('chatbot_id', chatbotId)
      .order('started_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  },

  // Get messages for conversation
  async getMessages(conversationId: string) {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('timestamp', { ascending: true })

    if (error) throw error
    return data
  },

  // Create new conversation
  async create(conversation: Tables['conversations']['Insert']) {
    const { data, error } = await supabase
      .from('conversations')
      .insert(conversation)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Add message to conversation
  async addMessage(message: Tables['messages']['Insert']) {
    const { data, error } = await supabase
      .from('messages')
      .insert(message)
      .select()
      .single()

    if (error) throw error
    return data
  }
}

// Upload API functions
export const uploadApi = {
  // Get uploads for chatbot
  async getForChatbot(chatbotId: string) {
    const { data, error } = await supabase
      .from('uploads')
      .select('*')
      .eq('chatbot_id', chatbotId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Upload file to storage
  async uploadFile(file: File, chatbotId: string) {
    const fileExt = file.name.split('.').pop()
    const fileName = `${chatbotId}/${Date.now()}.${fileExt}`

    // Upload to Supabase storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('chatbot-uploads')
      .upload(fileName, file)

    if (uploadError) throw uploadError

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('chatbot-uploads')
      .getPublicUrl(fileName)

    // Record upload in database
    const { data, error } = await supabase
      .from('uploads')
      .insert({
        chatbot_id: chatbotId,
        file_name: file.name,
        file_url: publicUrl,
        file_type: file.type,
        file_size: file.size,
        processed_status: 'pending'
      })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update upload status
  async updateStatus(id: string, status: 'pending' | 'processing' | 'completed' | 'failed', errorMessage?: string) {
    const { data, error } = await supabase
      .from('uploads')
      .update({ 
        processed_status: status,
        error_message: errorMessage || null
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }
}

// Analytics API functions
export const analyticsApi = {
  // Get dashboard stats for user
  async getDashboardStats() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    // Get user's chatbots
    const { data: chatbots } = await supabase
      .from('chatbots')
      .select('id, message_count, conversation_count')
      .eq('user_id', user.id)

    if (!chatbots) return { totalChatbots: 0, totalMessages: 0, totalConversations: 0 }

    const totalChatbots = chatbots.length
    const totalMessages = chatbots.reduce((sum, bot) => sum + bot.message_count, 0)
    const totalConversations = chatbots.reduce((sum, bot) => sum + bot.conversation_count, 0)

    return {
      totalChatbots,
      totalMessages,
      totalConversations
    }
  },

  // Record daily analytics
  async recordDailyStats(chatbotId: string, stats: Partial<Analytics>) {
    const today = new Date().toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('analytics')
      .upsert({
        chatbot_id: chatbotId,
        date: today,
        ...stats
      })
      .select()
      .single()

    if (error) throw error
    return data
  }
}

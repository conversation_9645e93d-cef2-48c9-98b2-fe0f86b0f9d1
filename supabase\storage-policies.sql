-- Storage policies for chatbot-uploads bucket
-- Run this in Supabase SQL Editor after creating the storage bucket

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy 1: Allow authenticated users to upload files to their own folder
CREATE POLICY "Authenticated users can upload files" ON storage.objects
  FOR INSERT 
  WITH CHECK (
    auth.role() = 'authenticated' 
    AND bucket_id = 'chatbot-uploads'
  );

-- Policy 2: Allow users to view files in their own chatbot folders
CREATE POLICY "Users can view own chatbot files" ON storage.objects
  FOR SELECT 
  USING (
    bucket_id = 'chatbot-uploads' 
    AND (
      -- Allow public access to files (since bucket is public)
      auth.role() = 'anon'
      OR 
      -- Allow authenticated users to see their own files
      (auth.role() = 'authenticated' AND auth.uid()::text = (storage.foldername(name))[1])
    )
  );

-- Policy 3: Allow users to update their own files
CREATE POLICY "Users can update own files" ON storage.objects
  FOR UPDATE 
  USING (
    auth.role() = 'authenticated' 
    AND bucket_id = 'chatbot-uploads'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Policy 4: Allow users to delete their own files
CREATE POLICY "Users can delete own files" ON storage.objects
  FOR DELETE 
  USING (
    auth.role() = 'authenticated' 
    AND bucket_id = 'chatbot-uploads'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Test the storage setup
-- This should work after creating the bucket
SELECT 
  'Storage bucket test' as test_name,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM storage.buckets WHERE name = 'chatbot-uploads'
    ) THEN 'Bucket exists ✅'
    ELSE 'Bucket missing ❌'
  END as result;

-- RLS Diagnostics for Chatbot Creator
-- Run this in Supabase SQL Editor to check RLS configuration

-- 1. Check if R<PERSON> is enabled on all tables
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled,
  CASE 
    WHEN rowsecurity THEN '✅ Enabled'
    ELSE '❌ Disabled'
  END as status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'chatbots', 'conversations', 'messages', 'analytics', 'knowledge_base', 'uploads', 'subscriptions')
ORDER BY tablename;

-- 2. Check existing policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 3. Test current user context
SELECT 
  'Current auth context' as test,
  auth.uid() as user_id,
  auth.role() as user_role;

-- 4. Test unauthenticated access to chatbots (should be blocked)
SELECT 
  'Unauthenticated chatbot access test' as test,
  COUNT(*) as chatbot_count,
  CASE 
    WHEN COUNT(*) = 0 THEN '✅ RLS working - no data returned'
    ELSE '❌ RLS not working - data accessible'
  END as result
FROM chatbots;

-- 5. Test public access to conversations (should work)
SELECT 
  'Public conversation access test' as test,
  COUNT(*) as conversation_count,
  CASE 
    WHEN COUNT(*) > 0 THEN '✅ Public access working'
    ELSE '⚠️ No conversations found or access blocked'
  END as result
FROM conversations;

-- 6. Check if functions exist
SELECT 
  'Functions check' as test,
  routine_name,
  routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('handle_new_user', 'update_message_counts', 'generate_embed_code');

-- 7. Check if triggers exist
SELECT 
  'Triggers check' as test,
  trigger_name,
  event_manipulation,
  event_object_table
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
AND trigger_name IN ('on_auth_user_created', 'on_message_created');

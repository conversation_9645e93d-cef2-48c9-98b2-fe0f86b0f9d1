import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          plan_type: 'free' | 'pro' | 'enterprise'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          plan_type?: 'free' | 'pro' | 'enterprise'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          plan_type?: 'free' | 'pro' | 'enterprise'
          created_at?: string
          updated_at?: string
        }
      }
      chatbots: {
        Row: {
          id: string
          user_id: string
          name: string
          template_type: 'support' | 'lead_gen' | 'sales_funnel'
          appearance_settings: any
          embed_code: string
          message_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          template_type: 'support' | 'lead_gen' | 'sales_funnel'
          appearance_settings?: any
          embed_code?: string
          message_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          template_type?: 'support' | 'lead_gen' | 'sales_funnel'
          appearance_settings?: any
          embed_code?: string
          message_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      uploads: {
        Row: {
          id: string
          chatbot_id: string
          file_url: string
          file_type: string
          processed_status: 'pending' | 'processing' | 'completed' | 'failed'
          created_at: string
        }
        Insert: {
          id?: string
          chatbot_id: string
          file_url: string
          file_type: string
          processed_status?: 'pending' | 'processing' | 'completed' | 'failed'
          created_at?: string
        }
        Update: {
          id?: string
          chatbot_id?: string
          file_url?: string
          file_type?: string
          processed_status?: 'pending' | 'processing' | 'completed' | 'failed'
          created_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          chatbot_id: string
          timestamp: string
          user_input: string
          ai_response: string
          created_at: string
        }
        Insert: {
          id?: string
          chatbot_id: string
          timestamp?: string
          user_input: string
          ai_response: string
          created_at?: string
        }
        Update: {
          id?: string
          chatbot_id?: string
          timestamp?: string
          user_input?: string
          ai_response?: string
          created_at?: string
        }
      }
    }
  }
}

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          company: string | null
          plan_type: 'free' | 'pro' | 'enterprise'
          message_quota: number
          messages_used: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          company?: string | null
          plan_type?: 'free' | 'pro' | 'enterprise'
          message_quota?: number
          messages_used?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          company?: string | null
          plan_type?: 'free' | 'pro' | 'enterprise'
          message_quota?: number
          messages_used?: number
          created_at?: string
          updated_at?: string
        }
      }
      chatbots: {
        Row: {
          id: string
          user_id: string
          name: string
          template_type: 'support' | 'lead_gen' | 'sales_funnel'
          appearance_settings: {
            primaryColor: string
            greeting: string
            avatar: string
            position: string
            welcomeMessage?: string
          }
          training_status: 'pending' | 'training' | 'completed' | 'failed'
          embed_code: string | null
          is_active: boolean
          message_count: number
          conversation_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          template_type: 'support' | 'lead_gen' | 'sales_funnel'
          appearance_settings?: {
            primaryColor?: string
            greeting?: string
            avatar?: string
            position?: string
            welcomeMessage?: string
          }
          training_status?: 'pending' | 'training' | 'completed' | 'failed'
          embed_code?: string | null
          is_active?: boolean
          message_count?: number
          conversation_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          template_type?: 'support' | 'lead_gen' | 'sales_funnel'
          appearance_settings?: {
            primaryColor?: string
            greeting?: string
            avatar?: string
            position?: string
            welcomeMessage?: string
          }
          training_status?: 'pending' | 'training' | 'completed' | 'failed'
          embed_code?: string | null
          is_active?: boolean
          message_count?: number
          conversation_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      knowledge_base: {
        Row: {
          id: string
          chatbot_id: string
          content_type: 'text' | 'pdf' | 'url' | 'document'
          title: string | null
          content: string
          source_url: string | null
          processed_at: string
          created_at: string
        }
        Insert: {
          id?: string
          chatbot_id: string
          content_type: 'text' | 'pdf' | 'url' | 'document'
          title?: string | null
          content: string
          source_url?: string | null
          processed_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          chatbot_id?: string
          content_type?: 'text' | 'pdf' | 'url' | 'document'
          title?: string | null
          content?: string
          source_url?: string | null
          processed_at?: string
          created_at?: string
        }
      }
      uploads: {
        Row: {
          id: string
          chatbot_id: string
          file_name: string
          file_url: string
          file_type: string
          file_size: number | null
          processed_status: 'pending' | 'processing' | 'completed' | 'failed'
          error_message: string | null
          created_at: string
        }
        Insert: {
          id?: string
          chatbot_id: string
          file_name: string
          file_url: string
          file_type: string
          file_size?: number | null
          processed_status?: 'pending' | 'processing' | 'completed' | 'failed'
          error_message?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          chatbot_id?: string
          file_name?: string
          file_url?: string
          file_type?: string
          file_size?: number | null
          processed_status?: 'pending' | 'processing' | 'completed' | 'failed'
          error_message?: string | null
          created_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          chatbot_id: string
          session_id: string
          visitor_id: string | null
          visitor_email: string | null
          visitor_name: string | null
          started_at: string
          ended_at: string | null
          message_count: number
          lead_captured: boolean
          created_at: string
        }
        Insert: {
          id?: string
          chatbot_id: string
          session_id: string
          visitor_id?: string | null
          visitor_email?: string | null
          visitor_name?: string | null
          started_at?: string
          ended_at?: string | null
          message_count?: number
          lead_captured?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          chatbot_id?: string
          session_id?: string
          visitor_id?: string | null
          visitor_email?: string | null
          visitor_name?: string | null
          started_at?: string
          ended_at?: string | null
          message_count?: number
          lead_captured?: boolean
          created_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          conversation_id: string
          chatbot_id: string
          content: string
          sender_type: 'user' | 'bot'
          metadata: Record<string, any>
          timestamp: string
          created_at: string
        }
        Insert: {
          id?: string
          conversation_id: string
          chatbot_id: string
          content: string
          sender_type: 'user' | 'bot'
          metadata?: Record<string, any>
          timestamp?: string
          created_at?: string
        }
        Update: {
          id?: string
          conversation_id?: string
          chatbot_id?: string
          content?: string
          sender_type?: 'user' | 'bot'
          metadata?: Record<string, any>
          timestamp?: string
          created_at?: string
        }
      }
      analytics: {
        Row: {
          id: string
          chatbot_id: string
          date: string
          total_conversations: number
          total_messages: number
          unique_visitors: number
          leads_captured: number
          avg_conversation_length: number
          created_at: string
        }
        Insert: {
          id?: string
          chatbot_id: string
          date: string
          total_conversations?: number
          total_messages?: number
          unique_visitors?: number
          leads_captured?: number
          avg_conversation_length?: number
          created_at?: string
        }
        Update: {
          id?: string
          chatbot_id?: string
          date?: string
          total_conversations?: number
          total_messages?: number
          unique_visitors?: number
          leads_captured?: number
          avg_conversation_length?: number
          created_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          plan_type: 'free' | 'pro' | 'enterprise'
          status: 'active' | 'canceled' | 'past_due' | 'unpaid'
          current_period_start: string | null
          current_period_end: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          plan_type: 'free' | 'pro' | 'enterprise'
          status: 'active' | 'canceled' | 'past_due' | 'unpaid'
          current_period_start?: string | null
          current_period_end?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          plan_type?: 'free' | 'pro' | 'enterprise'
          status?: 'active' | 'canceled' | 'past_due' | 'unpaid'
          current_period_start?: string | null
          current_period_end?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

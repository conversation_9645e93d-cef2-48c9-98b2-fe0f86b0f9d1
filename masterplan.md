# 📄 Masterplan: Chatbot Creator

## App Overview and Objectives
Chatbot Creator is a SaaS web application that empowers non-technical users — especially small business owners, marketers, and SaaS startups — to build, customize, and deploy AI-powered chatbots for customer support, lead generation, and sales funnels. The tool streamlines the chatbot creation process by offering pre-built templates, easy content training via file uploads or URLs, and automatic deployment tools like embeddable widgets.

## Target Audience
- Small business owners looking for low-cost automation
- Marketing teams focused on lead generation and customer interaction
- SaaS startups wanting quick support solutions without engineering effort

## Core Features and Functionality
- **Homepage with clear CTA** and visual previews
- **Dashboard** with chatbot management and performance stats
- **Chatbot Builder Flow** with 5 intuitive steps:
  1. Template selection
  2. Content upload (PDF, Markdown, CSV, URLs)
  3. AI training from uploaded content
  4. Chatbot customization (name, avatar, greeting, colors)
  5. Embeddable JS snippet generation
- **Chatbot Preview & Testing Page** with real-time behavior testing
- **Authentication system** with email/password login
- **Account settings and billing management**
- **Free and Paid Plans** with limits (50 messages for free users)
- **Built-in AI APIs (OpenAI, Gemini, DeepSeek)** for model selection without requiring user keys

## High-Level Technical Stack
- **Frontend:** React, TypeScript, Tailwind CSS, shadcn/ui, Vite
- **Backend:** Supabase (auth, database, storage)
- **Authentication:** Email/password via Supabase
- **AI Integration:** OpenAI API, Gemini API, DeepSeek API (server-side integration)
- **Deployment:** Vercel or Netlify (recommended for frontend); Supabase backend hosting

## Conceptual Data Model
- **Users**: id, email, password, plan_type
- **Chatbots**: id, user_id, name, template_type, appearance_settings, embed_code, message_count
- **Uploads**: id, chatbot_id, file_url, file_type, processed_status
- **Conversations**: id, chatbot_id, timestamp, user_input, ai_response

## User Interface Design Principles
- Clean, modern, mobile-first design
- Use of whitespace and card-based layouts
- Primary color: Blue-600 (#2563eb) for trust and professionalism
- Accent color: Sky-400 (#38bdf8) for highlights and CTAs
- Font: Inter for a modern, readable look

## Security Considerations
- Secure auth with Supabase (password encryption, token-based sessions)
- Role-based access only to user-owned chatbots
- Upload and file storage hygiene (PDF parsing, size limits)
- API request throttling to avoid abuse on free tier

## Development Phases or Milestones
**Phase 1: MVP**
- Auth, dashboard, chatbot builder (core flow), preview, and embed
- Limit: 1 chatbot per user, 50 messages

**Phase 2: Paid Plan Integration + AI Options**
- Billing system (Stripe or Paddle)
- Unlock higher message limits and chatbot count

**Phase 3: Enhanced Analytics and CMS Tools**
- User engagement stats (CTR, avg response time)
- Ability to update bot behavior dynamically

## Potential Challenges and Solutions
- **Training from diverse file types**: Use robust preprocessing/parsing logic
- **AI costs on free tier**: Implement message quotas + graceful error handling
- **Latency in chatbot responses**: Use edge functions or streaming responses where possible

## Future Expansion Possibilities
- Team accounts and collaboration
- Multilingual chatbot support
- Chat history export and advanced analytics


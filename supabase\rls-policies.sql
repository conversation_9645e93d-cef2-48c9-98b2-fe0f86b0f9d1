-- Row Level Security (RLS) Policies for Chatbot Creator
-- Run this SQL after creating the schema

-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chatbots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.knowledge_base ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Chatbots table policies
CREATE POLICY "Users can view own chatbots" ON public.chatbots
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own chatbots" ON public.chatbots
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own chatbots" ON public.chatbots
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own chatbots" ON public.chatbots
  FOR DELETE USING (auth.uid() = user_id);

-- Knowledge base policies
CREATE POLICY "Users can view knowledge base for own chatbots" ON public.knowledge_base
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = knowledge_base.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert knowledge base for own chatbots" ON public.knowledge_base
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = knowledge_base.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update knowledge base for own chatbots" ON public.knowledge_base
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = knowledge_base.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete knowledge base for own chatbots" ON public.knowledge_base
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = knowledge_base.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Uploads policies
CREATE POLICY "Users can view uploads for own chatbots" ON public.uploads
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = uploads.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert uploads for own chatbots" ON public.uploads
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = uploads.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update uploads for own chatbots" ON public.uploads
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = uploads.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete uploads for own chatbots" ON public.uploads
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = uploads.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Conversations policies (users can view conversations for their chatbots)
CREATE POLICY "Users can view conversations for own chatbots" ON public.conversations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = conversations.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Allow public insert for conversations (website visitors can start conversations)
CREATE POLICY "Anyone can create conversations" ON public.conversations
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update conversations for own chatbots" ON public.conversations
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = conversations.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Messages policies
CREATE POLICY "Users can view messages for own chatbots" ON public.messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = messages.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Allow public insert for messages (website visitors can send messages)
CREATE POLICY "Anyone can create messages" ON public.messages
  FOR INSERT WITH CHECK (true);

-- Analytics policies
CREATE POLICY "Users can view analytics for own chatbots" ON public.analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = analytics.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert analytics for own chatbots" ON public.analytics
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = analytics.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update analytics for own chatbots" ON public.analytics
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.chatbots 
      WHERE chatbots.id = analytics.chatbot_id 
      AND chatbots.user_id = auth.uid()
    )
  );

-- Subscriptions policies
CREATE POLICY "Users can view own subscription" ON public.subscriptions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscription" ON public.subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subscription" ON public.subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update message counts
CREATE OR REPLACE FUNCTION public.update_message_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update conversation message count
  UPDATE public.conversations 
  SET message_count = message_count + 1
  WHERE id = NEW.conversation_id;
  
  -- Update chatbot message count
  UPDATE public.chatbots 
  SET message_count = message_count + 1
  WHERE id = NEW.chatbot_id;
  
  -- Update user messages used (only for bot messages to avoid double counting)
  IF NEW.sender_type = 'bot' THEN
    UPDATE public.users 
    SET messages_used = messages_used + 1
    WHERE id = (
      SELECT user_id FROM public.chatbots 
      WHERE id = NEW.chatbot_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update message counts when new message is created
CREATE TRIGGER on_message_created
  AFTER INSERT ON public.messages
  FOR EACH ROW EXECUTE FUNCTION public.update_message_counts();

-- Function to generate embed code
CREATE OR REPLACE FUNCTION public.generate_embed_code(chatbot_uuid UUID)
RETURNS TEXT AS $$
BEGIN
  RETURN '<script src="https://chatbot-creator.com/embed.js" data-chatbot-id="' || chatbot_uuid || '"></script>';
END;
$$ LANGUAGE plpgsql;

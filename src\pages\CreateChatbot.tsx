import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Bo<PERSON>, ArrowLeft, ArrowRight, CheckCircle } from 'lucide-react'

const steps = [
  { id: 1, name: 'Template', description: 'Choose a chatbot template' },
  { id: 2, name: 'Content', description: 'Upload training content' },
  { id: 3, name: 'Training', description: 'Train your AI model' },
  { id: 4, name: 'Customize', description: 'Customize appearance' },
  { id: 5, name: 'Deploy', description: 'Get embed code' },
]

export default function CreateChatbot() {
  const [currentStep, setCurrentStep] = useState(1)

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/dashboard" className="flex items-center">
                <ArrowLeft className="h-5 w-5 text-gray-600 mr-2" />
                <Bot className="h-8 w-8 text-blue-600" />
                <span className="ml-2 text-xl font-bold text-gray-900">Chatbot Creator</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create New Chatbot</h1>
          <p className="text-gray-600 mt-2">Follow the steps below to create your AI-powered chatbot</p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep > step.id 
                    ? 'bg-green-500 border-green-500 text-white' 
                    : currentStep === step.id 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-500'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="h-6 w-6" />
                  ) : (
                    <span className="text-sm font-medium">{step.id}</span>
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <Card>
          <CardHeader>
            <CardTitle>Step {currentStep}: {steps[currentStep - 1].name}</CardTitle>
            <CardDescription>{steps[currentStep - 1].description}</CardDescription>
          </CardHeader>
          <CardContent>
            {currentStep === 1 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Choose a Template</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <Card className="cursor-pointer hover:shadow-md transition-shadow border-2 border-blue-200">
                    <CardHeader>
                      <CardTitle className="text-lg">Support Bot</CardTitle>
                      <CardDescription>
                        Handle customer support queries and provide instant help
                      </CardDescription>
                    </CardHeader>
                  </Card>
                  <Card className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader>
                      <CardTitle className="text-lg">Lead Gen Bot</CardTitle>
                      <CardDescription>
                        Capture leads and qualify potential customers
                      </CardDescription>
                    </CardHeader>
                  </Card>
                  <Card className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader>
                      <CardTitle className="text-lg">Sales Funnel Bot</CardTitle>
                      <CardDescription>
                        Guide visitors through your sales process
                      </CardDescription>
                    </CardHeader>
                  </Card>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Upload Training Content</h3>
                <p className="text-gray-600">
                  Upload documents, PDFs, or provide website URLs to train your chatbot
                </p>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <p className="text-gray-500">Drag and drop files here, or click to browse</p>
                  <Button variant="outline" className="mt-4">Choose Files</Button>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Training Your AI Model</h3>
                <p className="text-gray-600">
                  Your chatbot is being trained on the uploaded content. This may take a few minutes.
                </p>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className="ml-3 text-blue-800">Training in progress...</span>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Customize Your Chatbot</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Chatbot Name
                      </label>
                      <input 
                        type="text" 
                        className="w-full p-2 border border-gray-300 rounded-md"
                        placeholder="My Support Bot"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Greeting Message
                      </label>
                      <textarea 
                        className="w-full p-2 border border-gray-300 rounded-md"
                        rows={3}
                        placeholder="Hi! How can I help you today?"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Primary Color
                      </label>
                      <input 
                        type="color" 
                        className="w-full h-10 border border-gray-300 rounded-md"
                        defaultValue="#2563eb"
                      />
                    </div>
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <h4 className="font-medium mb-2">Preview</h4>
                    <div className="bg-white rounded-lg shadow-sm p-4">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                          <Bot className="h-4 w-4 text-white" />
                        </div>
                        <span className="ml-2 font-medium">My Support Bot</span>
                      </div>
                      <p className="text-sm text-gray-600">Hi! How can I help you today?</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 5 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Deploy Your Chatbot</h3>
                <p className="text-gray-600">
                  Your chatbot is ready! Copy the embed code below and add it to your website.
                </p>
                <div className="bg-gray-100 p-4 rounded-lg">
                  <code className="text-sm">
                    {`<script src="https://chatbot-creator.com/embed.js" data-chatbot-id="abc123"></script>`}
                  </code>
                </div>
                <Button>Copy Embed Code</Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button 
            variant="outline" 
            onClick={prevStep} 
            disabled={currentStep === 1}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            disabled={currentStep === steps.length}
          >
            {currentStep === steps.length ? 'Complete' : 'Next'}
            {currentStep < steps.length && <ArrowRight className="h-4 w-4 ml-2" />}
          </Button>
        </div>
      </div>
    </div>
  )
}
